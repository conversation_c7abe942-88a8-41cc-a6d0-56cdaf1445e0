@echo off
echo Testing compilation...

REM Try to find arm-none-eabi-gcc in common locations
set GCC_PATH=""

if exist "C:\Program Files (x86)\GNU Arm Embedded Toolchain\*\bin\arm-none-eabi-gcc.exe" (
    for /d %%i in ("C:\Program Files (x86)\GNU Arm Embedded Toolchain\*") do (
        set GCC_PATH="%%i\bin\arm-none-eabi-gcc.exe"
        goto found
    )
)

if exist "C:\Program Files\GNU Arm Embedded Toolchain\*\bin\arm-none-eabi-gcc.exe" (
    for /d %%i in ("C:\Program Files\GNU Arm Embedded Toolchain\*") do (
        set GCC_PATH="%%i\bin\arm-none-eabi-gcc.exe"
        goto found
    )
)

REM Check STM32CubeCLT paths
if exist "E:\hunna\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe" (
    set GCC_PATH="E:\hunna\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe"
    goto found
)

echo GCC not found in standard locations
goto end

:found
echo Found GCC at: %GCC_PATH%
%GCC_PATH% --version

REM Test compile a simple file
echo Testing simple compilation...
%GCC_PATH% -c -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -I"../CM7/Core/Inc" -I"../Drivers/STM32H7xx_HAL_Driver/Inc" -I"../Drivers/CMSIS/Device/ST/STM32H7xx/Include" -I"../Drivers/CMSIS/Include" -O0 -g3 -Wall -fdata-sections -ffunction-sections -c -fmessage-length=0 CM7/Core/Src/main.c -o test_main.o

if %ERRORLEVEL% EQU 0 (
    echo Compilation successful!
    del test_main.o
) else (
    echo Compilation failed with errors
)

:end
pause
