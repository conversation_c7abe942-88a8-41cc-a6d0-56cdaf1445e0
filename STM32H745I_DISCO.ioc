#MicroXplorer Configuration settings - do not modify
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_17
ADC1.ContinuousConvMode=ENABLE
ADC1.EnableAnalogWatchDog1=false
ADC1.IPParameters=Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,OffsetSignedSaturation-1\#ChannelRegularConversion,NbrOfConversionFlag,ContinuousConvMode,NbrOfConversion,EnableAnalogWatchDog1,master
ADC1.NbrOfConversion=1
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetSignedSaturation-1\#ChannelRegularConversion=DISABLE
ADC1.Rank-1\#ChannelRegularConversion=1
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
CORTEX_M7.AccessPermission-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_REGION_FULL_ACCESS
CORTEX_M7.BaseAddress-Cortex_Memory_Protection_Unit_Region0_Settings=0xD0000000
CORTEX_M7.BaseAddress-Cortex_Memory_Protection_Unit_Region1_Settings=0x90000000
CORTEX_M7.CPU_DCache=Enabled
CORTEX_M7.CPU_ICache=Enabled
CORTEX_M7.DisableExec-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_INSTRUCTION_ACCESS_DISABLE
CORTEX_M7.DisableExec-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_INSTRUCTION_ACCESS_DISABLE
CORTEX_M7.Enable-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_REGION_ENABLE
CORTEX_M7.Enable-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_REGION_ENABLE
CORTEX_M7.Enable-Cortex_Memory_Protection_Unit_Region2_Settings=__NULL
CORTEX_M7.Enable-Cortex_Memory_Protection_Unit_Region3_Settings=__NULL
CORTEX_M7.Enable-Cortex_Memory_Protection_Unit_Region4_Settings=__NULL
CORTEX_M7.IPParameters=MPU_Control,Enable-Cortex_Memory_Protection_Unit_Region0_Settings,BaseAddress-Cortex_Memory_Protection_Unit_Region0_Settings,Size-Cortex_Memory_Protection_Unit_Region0_Settings,Enable-Cortex_Memory_Protection_Unit_Region1_Settings,Enable-Cortex_Memory_Protection_Unit_Region2_Settings,Enable-Cortex_Memory_Protection_Unit_Region3_Settings,Enable-Cortex_Memory_Protection_Unit_Region4_Settings,CPU_ICache,CPU_DCache,DisableExec-Cortex_Memory_Protection_Unit_Region0_Settings,IsCacheable-Cortex_Memory_Protection_Unit_Region0_Settings,IsBufferable-Cortex_Memory_Protection_Unit_Region0_Settings,AccessPermission-Cortex_Memory_Protection_Unit_Region0_Settings,BaseAddress-Cortex_Memory_Protection_Unit_Region1_Settings,Size-Cortex_Memory_Protection_Unit_Region1_Settings,AccessPermission-Cortex_Memory_Protection_Unit_Region1_Settings,IsShareable-Cortex_Memory_Protection_Unit_Region1_Settings,DisableExec-Cortex_Memory_Protection_Unit_Region1_Settings,IsCacheable-Cortex_Memory_Protection_Unit_Region1_Settings,IsBufferable-Cortex_Memory_Protection_Unit_Region1_Settings
CORTEX_M7.IsBufferable-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_ACCESS_BUFFERABLE
CORTEX_M7.IsBufferable-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_ACCESS_BUFFERABLE
CORTEX_M7.IsCacheable-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_ACCESS_CACHEABLE
CORTEX_M7.IsCacheable-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_ACCESS_CACHEABLE
CORTEX_M7.IsShareable-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_ACCESS_NOT_SHAREABLE
CORTEX_M7.MPU_Control=MPU_PRIVILEGED_DEFAULT
CORTEX_M7.Size-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_REGION_SIZE_1MB
CORTEX_M7.Size-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_REGION_SIZE_128MB
CortexM4.IPs=CORTEX_M4\:I,DEBUG,FATFS_M4\:I,FREERTOS_M4\:I,IWDG2\:I,OPENAMP_M4\:I,PDM2PCM_M4\:I,PWR,RCC,RESMGR_UTILITY,SYS_M4\:I,USB_DEVICE_M4\:I,USB_HOST_M4\:I,VREFBUF,WWDG2\:I,DMA,BDMA,MDMA,NVIC2\:I,GPIO
CortexM7.IPs=CORTEX_M7\:I,DEBUG\:I,FATFS_M7\:I,FREERTOS_M7\:I,IWDG1\:I,OPENAMP_M7\:I,PDM2PCM_M7\:I,PWR\:I,RCC\:I,RESMGR_UTILITY\:I,SYS\:I,USB_DEVICE_M7\:I,USB_HOST_M7\:I,VREFBUF\:I,WWDG1\:I,DMA\:I,BDMA\:I,MDMA\:I,NVIC1\:I,TIM6\:I,CRC\:I,LTDC\:I,DMA2D\:I,FMC\:I,QUADSPI\:I,STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7\:I,GPIO\:I,JPEG\:I,ADC1\:I
CortexM7.Pins=PB6,PB7,PG3,PA6
DMA2D.ColorMode=DMA2D_OUTPUT_RGB565
DMA2D.IPParameters=Mode,ColorMode
DMA2D.Mode=DMA2D_R2M
Dma.ADC1.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.0.EventEnable=DISABLE
Dma.ADC1.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.0.Instance=DMA1_Stream0
Dma.ADC1.0.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC1.0.MemInc=DMA_MINC_ENABLE
Dma.ADC1.0.Mode=DMA_CIRCULAR
Dma.ADC1.0.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.0.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC1.0.Priority=DMA_PRIORITY_LOW
Dma.ADC1.0.RequestNumber=1
Dma.ADC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC1.0.SignalID=NONE
Dma.ADC1.0.SyncEnable=DISABLE
Dma.ADC1.0.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC1.0.SyncRequestNumber=1
Dma.ADC1.0.SyncSignalID=NONE
Dma.Request0=ADC1
Dma.RequestsNb=1
FMC.CASLatency2=FMC_SDRAM_CAS_LATENCY_2
FMC.ExitSelfRefreshDelay2=6
FMC.IPParameters=ReadBurst2,CASLatency2,SDClockPeriod2,ExitSelfRefreshDelay2,SelfRefreshTime2,RowCycleDelay2,RowCycleDelay1,WriteRecoveryTime2,RCDDelay2,RPDelay2,RPDelay1,LoadToActiveDelay2
FMC.LoadToActiveDelay2=2
FMC.RCDDelay2=2
FMC.RPDelay1=2
FMC.RPDelay2=2
FMC.ReadBurst2=FMC_SDRAM_RBURST_ENABLE
FMC.RowCycleDelay1=6
FMC.RowCycleDelay2=6
FMC.SDClockPeriod2=FMC_SDRAM_CLOCK_PERIOD_2
FMC.SelfRefreshTime2=4
FMC.WriteRecoveryTime2=2
FREERTOS_M7.FootprintOK=true
FREERTOS_M7.IPParameters=Tasks01,FootprintOK,configTOTAL_HEAP_SIZE,configUSE_NEWLIB_REENTRANT
FREERTOS_M7.Tasks01=defaultTask,8,128,StartDefaultTask,Default,NULL,Dynamic,NULL,NULL;TouchGFXTask,24,3048,TouchGFX_Task,As weak,NULL,Dynamic,NULL,NULL
FREERTOS_M7.configTOTAL_HEAP_SIZE=0x8000
FREERTOS_M7.configUSE_NEWLIB_REENTRANT=0
File.Version=6
GPIO.groupedBy=Group By Peripherals
JPEG.IPParameters=USE_JPEG_ENCODER,JPEG_RGB_FORMAT
JPEG.JPEG_RGB_FORMAT=JPEG_RGB565
JPEG.USE_JPEG_ENCODER=0
KeepUserPlacement=false
LTDC.ActiveH=272
LTDC.ActiveW=480
LTDC.Alpha0_L0=0
LTDC.Alpha_L0=255
LTDC.BlendingFactor1_L0=LTDC_BLENDING_FACTOR1_CA
LTDC.BlendingFactor2_L0=LTDC_BLENDING_FACTOR2_CA
LTDC.FBStartAdress_L0=0xD0000000
LTDC.HBP=13
LTDC.HFP=32
LTDC.HSync=41
LTDC.IPParameters=Layers,WindowX1_L0,WindowY1_L0,Alpha_L0,Alpha0_L0,ImageWidth_L0,ImageHeight_L0,FBStartAdress_L0,PixelFormat_L0,HSync,HBP,ActiveW,VSync,VBP,ActiveH,HFP,BlendingFactor1_L0,BlendingFactor2_L0
LTDC.ImageHeight_L0=272
LTDC.ImageWidth_L0=480
LTDC.Layers=0
LTDC.PixelFormat_L0=LTDC_PIXEL_FORMAT_RGB565
LTDC.VBP=2
LTDC.VSync=10
LTDC.WindowX1_L0=480
LTDC.WindowY1_L0=272
Mcu.CPN=STM32H745XIH3
Mcu.Context0=CortexM7
Mcu.Context1=CortexM4
Mcu.ContextNb=2
Mcu.Family=STM32H7
Mcu.IP0=ADC1
Mcu.IP1=CORTEX_M4
Mcu.IP10=MDMA
Mcu.IP11=NVIC1
Mcu.IP12=NVIC2
Mcu.IP13=QUADSPI
Mcu.IP14=RCC
Mcu.IP15=SYS_M4
Mcu.IP16=SYS
Mcu.IP2=CORTEX_M7
Mcu.IP3=CRC
Mcu.IP4=DMA
Mcu.IP5=DMA2D
Mcu.IP6=FMC
Mcu.IP7=FREERTOS_M7
Mcu.IP8=JPEG
Mcu.IP9=LTDC
Mcu.IPNb=17
Mcu.Name=STM32H745XIHx
Mcu.Package=TFBGA240
Mcu.Pin0=PK5
Mcu.Pin1=PG9
Mcu.Pin10=PK6
Mcu.Pin11=PK3
Mcu.Pin12=PG15
Mcu.Pin13=PK7
Mcu.Pin14=PG14
Mcu.Pin15=PJ14
Mcu.Pin16=PJ12
Mcu.Pin17=PD0
Mcu.Pin18=PI9
Mcu.Pin19=PJ13
Mcu.Pin2=PI1
Mcu.Pin20=PD1
Mcu.Pin21=PA8
Mcu.Pin22=PG8
Mcu.Pin23=PF2
Mcu.Pin24=PF1
Mcu.Pin25=PF0
Mcu.Pin26=PG5
Mcu.Pin27=PG6
Mcu.Pin28=PI12
Mcu.Pin29=PI14
Mcu.Pin3=PI0
Mcu.Pin30=PF3
Mcu.Pin31=PG4
Mcu.Pin32=PG3
Mcu.Pin33=PK2
Mcu.Pin34=PH1-OSC_OUT (PH1)
Mcu.Pin35=PH0-OSC_IN (PH0)
Mcu.Pin36=PF5
Mcu.Pin37=PF4
Mcu.Pin38=PF6
Mcu.Pin39=PF7
Mcu.Pin4=PE1
Mcu.Pin40=PJ11
Mcu.Pin41=PC0
Mcu.Pin42=PF10
Mcu.Pin43=PF9
Mcu.Pin44=PJ10
Mcu.Pin45=PJ9
Mcu.Pin46=PH2
Mcu.Pin47=PA1
Mcu.Pin48=PA0
Mcu.Pin49=PJ0
Mcu.Pin5=PB6
Mcu.Pin50=PE10
Mcu.Pin51=PJ8
Mcu.Pin52=PJ7
Mcu.Pin53=PJ6
Mcu.Pin54=PH3
Mcu.Pin55=PH5
Mcu.Pin56=PI15
Mcu.Pin57=PJ1
Mcu.Pin58=PF13
Mcu.Pin59=PF14
Mcu.Pin6=PK4
Mcu.Pin60=PE9
Mcu.Pin61=PE11
Mcu.Pin62=PD15
Mcu.Pin63=PD14
Mcu.Pin64=PA6
Mcu.Pin65=PF12
Mcu.Pin66=PF15
Mcu.Pin67=PE12
Mcu.Pin68=PE15
Mcu.Pin69=PJ5
Mcu.Pin7=PJ15
Mcu.Pin70=PH9
Mcu.Pin71=PD11
Mcu.Pin72=PF11
Mcu.Pin73=PG0
Mcu.Pin74=PE8
Mcu.Pin75=PE13
Mcu.Pin76=PH6
Mcu.Pin77=PD10
Mcu.Pin78=PD9
Mcu.Pin79=PJ3
Mcu.Pin8=PE0
Mcu.Pin80=PJ4
Mcu.Pin81=PG1
Mcu.Pin82=PE7
Mcu.Pin83=PE14
Mcu.Pin84=PH7
Mcu.Pin85=PD8
Mcu.Pin86=VP_CRC_VS_CRC
Mcu.Pin87=VP_DMA2D_VS_DMA2D
Mcu.Pin88=VP_FREERTOS_M7_VS_CMSIS_V2
Mcu.Pin89=VP_JPEG_VS_JPEG
Mcu.Pin9=PB7
Mcu.Pin90=VP_SYS_VS_tim6
Mcu.Pin91=VP_SYS_M4_VS_Systick
Mcu.Pin92=VP_STMicroelectronics.X-CUBE-TOUCHGFX_M7_VS_GraphicsJjApplication_4.25.0
Mcu.PinsNb=93
Mcu.ThirdParty0=STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0
Mcu.ThirdParty0_ContextShortName=M7
Mcu.ThirdParty0_Instance=STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7
Mcu.ThirdPartyNb=1
Mcu.UserConstants=
Mcu.UserName=STM32H745XIHx
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.BlockCount=0
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.BlockDataLength=0
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.BufferTransferLength=32
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.CircularMode=MDMA_LINEAR_LIST
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.DataAlignment=MDMA_DATAALIGN_PACKENABLE
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.DestBlockAddressOffset=0
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.DestBurst=MDMA_DEST_BURST_32BEATS
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.DestDataSize=MDMA_DEST_DATASIZE_BYTE
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.DestinationInc=MDMA_DEST_INC_BYTE
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.DstAddress=0
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.Endianness=MDMA_LITTLE_ENDIANNESS_PRESERVE
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.Instance=MDMA_Channel6
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.MaskAddress=0
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.MaskData=0
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.Priority=MDMA_PRIORITY_VERY_HIGH
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.Rank=First
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.RequestParameters=Instance,CircularMode,TransferTriggerMode,Priority,Endianness,SourceInc,DestinationInc,SourceDataSize,DestDataSize,DataAlignment,BufferTransferLength,SourceBurst,DestBurst,SourceBlockAddressOffset,DestBlockAddressOffset,MaskAddress,MaskData,SrcAddress,DstAddress,BlockDataLength,BlockCount,Rank
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.SourceBlockAddressOffset=0
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.SourceBurst=MDMA_SOURCE_BURST_32BEATS
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.SourceDataSize=MDMA_SRC_DATASIZE_WORD
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.SourceInc=MDMA_SRC_INC_DISABLE
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.SrcAddress=0
Mdma.MDMA_Channel6.JPEG_OUTFIFO_TH.0.TransferTriggerMode=MDMA_BUFFER_TRANSFER
Mdma.MDMA_Channel6.Request0=JPEG_OUTFIFO_TH
Mdma.MDMA_Channel6.RequestsNb=1
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.BlockCount=0
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.BlockDataLength=0
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.BufferTransferLength=32
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.CircularMode=MDMA_LINEAR_LIST
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.DataAlignment=MDMA_DATAALIGN_PACKENABLE
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.DestBlockAddressOffset=0
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.DestBurst=MDMA_DEST_BURST_16BEATS
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.DestDataSize=MDMA_DEST_DATASIZE_WORD
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.DestinationInc=MDMA_DEST_INC_DISABLE
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.DstAddress=0
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.Endianness=MDMA_LITTLE_ENDIANNESS_PRESERVE
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.Instance=MDMA_Channel7
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.MaskAddress=0
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.MaskData=0
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.Priority=MDMA_PRIORITY_HIGH
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.Rank=First
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.RequestParameters=Instance,CircularMode,TransferTriggerMode,Priority,Endianness,SourceInc,DestinationInc,SourceDataSize,DestDataSize,DataAlignment,BufferTransferLength,SourceBurst,DestBurst,SourceBlockAddressOffset,DestBlockAddressOffset,MaskAddress,MaskData,SrcAddress,DstAddress,BlockDataLength,BlockCount,Rank
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.SourceBlockAddressOffset=0
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.SourceBurst=MDMA_SOURCE_BURST_32BEATS
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.SourceDataSize=MDMA_SRC_DATASIZE_BYTE
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.SourceInc=MDMA_SRC_INC_BYTE
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.SrcAddress=0
Mdma.MDMA_Channel7.JPEG_INFIFO_TH.0.TransferTriggerMode=MDMA_BUFFER_TRANSFER
Mdma.MDMA_Channel7.Request0=JPEG_INFIFO_TH
Mdma.MDMA_Channel7.RequestsNb=1
Mdma.RequestSet0=MDMA_Channel7
Mdma.RequestSet1=MDMA_Channel6
Mdma.RequestSetsNb=2
MxCube.Version=6.11.1
MxDb.Version=DB.6.0.111
NVIC1.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC1.DMA1_Stream0_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC1.DMA2D_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC1.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC1.ForceEnableDMAVector=true
NVIC1.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC1.JPEG_IRQn=true\:5\:0\:true\:false\:true\:true\:true\:true\:true
NVIC1.LTDC_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC1.MDMA_IRQn=true\:5\:0\:true\:false\:true\:true\:false\:true\:true
NVIC1.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC1.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC1.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC1.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC1.QUADSPI_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC1.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC1.SavedPendsvIrqHandlerGenerated=true
NVIC1.SavedSvcallIrqHandlerGenerated=true
NVIC1.SavedSystickIrqHandlerGenerated=true
NVIC1.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC1.TIM6_DAC_IRQn=true\:4\:0\:true\:false\:true\:false\:false\:true\:true
NVIC1.TimeBase=TIM6_DAC_IRQn
NVIC1.TimeBaseIP=TIM6
NVIC1.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC2.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.ForceEnableDMAVector=true
NVIC2.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC2.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC2.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0.GPIOParameters=PinAttribute
PA0.Locked=true
PA0.Mode=IN16-Single-Ended
PA0.PinAttribute=CortexM7
PA0.Signal=ADC1_INP16
PA1.GPIOParameters=PinAttribute
PA1.Locked=true
PA1.Mode=IN17-Single-Ended
PA1.PinAttribute=CortexM7
PA1.Signal=ADC1_INP17
PA6.ContextOwner=CortexM7
PA6.GPIOParameters=GPIO_Speed,GPIO_Label,PinAttribute
PA6.GPIO_Label=MCU_ACTIVE
PA6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA6.Locked=true
PA6.PinAttribute=CortexM7
PA6.Signal=GPIO_Output
PA8.GPIOParameters=PinAttribute
PA8.Mode=Clock-out-1
PA8.PinAttribute=CortexM7
PA8.Signal=RCC_MCO_1
PB6.ContextOwner=CortexM7
PB6.GPIOParameters=GPIO_Speed,GPIO_Label,PinAttribute
PB6.GPIO_Label=VSYNC_FREQ
PB6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB6.Locked=true
PB6.PinAttribute=CortexM7
PB6.Signal=GPIO_Output
PB7.ContextOwner=CortexM7
PB7.GPIOParameters=GPIO_Speed,GPIO_Label,GPIO_FM7,PinAttribute
PB7.GPIO_FM7=__NULL
PB7.GPIO_Label=RENDER_TIME
PB7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB7.Locked=true
PB7.PinAttribute=CortexM7
PB7.Signal=GPIO_Output
PC0.GPIOParameters=PinAttribute
PC0.Locked=true
PC0.PinAttribute=CortexM7
PC0.Signal=ADCx_INP10
PD0.GPIOParameters=PinAttribute
PD0.Locked=true
PD0.PinAttribute=CortexM7
PD0.Signal=FMC_D2_DA2
PD1.GPIOParameters=PinAttribute
PD1.Locked=true
PD1.PinAttribute=CortexM7
PD1.Signal=FMC_D3_DA3
PD10.GPIOParameters=PinAttribute
PD10.Locked=true
PD10.PinAttribute=CortexM7
PD10.Signal=FMC_D15_DA15
PD11.GPIOParameters=GPIO_Speed,PinAttribute
PD11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD11.Locked=true
PD11.Mode=Dual_Bank
PD11.PinAttribute=CortexM7
PD11.Signal=QUADSPI_BK1_IO0
PD14.GPIOParameters=PinAttribute
PD14.PinAttribute=CortexM7
PD14.Signal=FMC_D0_DA0
PD15.GPIOParameters=PinAttribute
PD15.Locked=true
PD15.PinAttribute=CortexM7
PD15.Signal=FMC_D1_DA1
PD8.GPIOParameters=PinAttribute
PD8.Locked=true
PD8.PinAttribute=CortexM7
PD8.Signal=FMC_D13_DA13
PD9.GPIOParameters=PinAttribute
PD9.Locked=true
PD9.PinAttribute=CortexM7
PD9.Signal=FMC_D14_DA14
PE0.GPIOParameters=PinAttribute
PE0.Locked=true
PE0.PinAttribute=CortexM7
PE0.Signal=FMC_NBL0
PE1.GPIOParameters=PinAttribute
PE1.Locked=true
PE1.PinAttribute=CortexM7
PE1.Signal=FMC_NBL1
PE10.GPIOParameters=PinAttribute
PE10.Locked=true
PE10.PinAttribute=CortexM7
PE10.Signal=FMC_D7_DA7
PE11.GPIOParameters=PinAttribute
PE11.Locked=true
PE11.PinAttribute=CortexM7
PE11.Signal=FMC_D8_DA8
PE12.GPIOParameters=PinAttribute
PE12.Locked=true
PE12.PinAttribute=CortexM7
PE12.Signal=FMC_D9_DA9
PE13.GPIOParameters=PinAttribute
PE13.Locked=true
PE13.PinAttribute=CortexM7
PE13.Signal=FMC_D10_DA10
PE14.GPIOParameters=PinAttribute
PE14.Locked=true
PE14.PinAttribute=CortexM7
PE14.Signal=FMC_D11_DA11
PE15.GPIOParameters=PinAttribute
PE15.Locked=true
PE15.PinAttribute=CortexM7
PE15.Signal=FMC_D12_DA12
PE7.GPIOParameters=PinAttribute
PE7.Locked=true
PE7.PinAttribute=CortexM7
PE7.Signal=FMC_D4_DA4
PE8.GPIOParameters=PinAttribute
PE8.Locked=true
PE8.PinAttribute=CortexM7
PE8.Signal=FMC_D5_DA5
PE9.GPIOParameters=PinAttribute
PE9.Locked=true
PE9.PinAttribute=CortexM7
PE9.Signal=FMC_D6_DA6
PF0.GPIOParameters=PinAttribute
PF0.PinAttribute=CortexM7
PF0.Signal=FMC_A0
PF1.GPIOParameters=PinAttribute
PF1.Locked=true
PF1.PinAttribute=CortexM7
PF1.Signal=FMC_A1
PF10.GPIOParameters=GPIO_Speed,PinAttribute
PF10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PF10.Locked=true
PF10.Mode=Dual_Bank
PF10.PinAttribute=CortexM7
PF10.Signal=QUADSPI_CLK
PF11.GPIOParameters=PinAttribute
PF11.Locked=true
PF11.PinAttribute=CortexM7
PF11.Signal=FMC_SDNRAS
PF12.GPIOParameters=PinAttribute
PF12.Locked=true
PF12.PinAttribute=CortexM7
PF12.Signal=FMC_A6
PF13.GPIOParameters=PinAttribute
PF13.Locked=true
PF13.PinAttribute=CortexM7
PF13.Signal=FMC_A7
PF14.GPIOParameters=PinAttribute
PF14.Locked=true
PF14.PinAttribute=CortexM7
PF14.Signal=FMC_A8
PF15.GPIOParameters=PinAttribute
PF15.Locked=true
PF15.PinAttribute=CortexM7
PF15.Signal=FMC_A9
PF2.GPIOParameters=PinAttribute
PF2.Locked=true
PF2.PinAttribute=CortexM7
PF2.Signal=FMC_A2
PF3.GPIOParameters=PinAttribute
PF3.Locked=true
PF3.PinAttribute=CortexM7
PF3.Signal=FMC_A3
PF4.GPIOParameters=PinAttribute
PF4.Locked=true
PF4.PinAttribute=CortexM7
PF4.Signal=FMC_A4
PF5.GPIOParameters=PinAttribute
PF5.Locked=true
PF5.PinAttribute=CortexM7
PF5.Signal=FMC_A5
PF6.GPIOParameters=GPIO_Speed,PinAttribute
PF6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PF6.Locked=true
PF6.Mode=Dual_Bank
PF6.PinAttribute=CortexM7
PF6.Signal=QUADSPI_BK1_IO3
PF7.GPIOParameters=GPIO_Speed,PinAttribute
PF7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PF7.Locked=true
PF7.Mode=Dual_Bank
PF7.PinAttribute=CortexM7
PF7.Signal=QUADSPI_BK1_IO2
PF9.GPIOParameters=GPIO_Speed,PinAttribute
PF9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PF9.Locked=true
PF9.Mode=Dual_Bank
PF9.PinAttribute=CortexM7
PF9.Signal=QUADSPI_BK1_IO1
PG0.GPIOParameters=PinAttribute
PG0.Locked=true
PG0.PinAttribute=CortexM7
PG0.Signal=FMC_A10
PG1.GPIOParameters=PinAttribute
PG1.Locked=true
PG1.PinAttribute=CortexM7
PG1.Signal=FMC_A11
PG14.GPIOParameters=GPIO_Speed,PinAttribute
PG14.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG14.Locked=true
PG14.Mode=Dual_Bank
PG14.PinAttribute=CortexM7
PG14.Signal=QUADSPI_BK2_IO3
PG15.GPIOParameters=PinAttribute
PG15.Locked=true
PG15.PinAttribute=CortexM7
PG15.Signal=FMC_SDNCAS
PG3.ContextOwner=CortexM7
PG3.GPIOParameters=GPIO_Speed,GPIO_Label,PinAttribute
PG3.GPIO_Label=FRAME_RATE
PG3.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG3.Locked=true
PG3.PinAttribute=CortexM7
PG3.Signal=GPIO_Output
PG4.GPIOParameters=PinAttribute
PG4.Locked=true
PG4.PinAttribute=CortexM7
PG4.Signal=FMC_A14_BA0
PG5.GPIOParameters=PinAttribute
PG5.Locked=true
PG5.PinAttribute=CortexM7
PG5.Signal=FMC_A15_BA1
PG6.GPIOParameters=GPIO_Speed,GPIO_PuPd,PinAttribute
PG6.GPIO_PuPd=GPIO_PULLUP
PG6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG6.Locked=true
PG6.Mode=Enable Chip Select 1 for both banks
PG6.PinAttribute=CortexM7
PG6.Signal=QUADSPI_BK1_NCS
PG8.GPIOParameters=PinAttribute
PG8.Locked=true
PG8.PinAttribute=CortexM7
PG8.Signal=FMC_SDCLK
PG9.GPIOParameters=GPIO_Speed,PinAttribute
PG9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG9.Locked=true
PG9.Mode=Dual_Bank
PG9.PinAttribute=CortexM7
PG9.Signal=QUADSPI_BK2_IO2
PH0-OSC_IN\ (PH0).GPIOParameters=PinAttribute
PH0-OSC_IN\ (PH0).Mode=HSE-External-Oscillator
PH0-OSC_IN\ (PH0).PinAttribute=CortexM7
PH0-OSC_IN\ (PH0).Signal=RCC_OSC_IN
PH1-OSC_OUT\ (PH1).GPIOParameters=PinAttribute
PH1-OSC_OUT\ (PH1).Mode=HSE-External-Oscillator
PH1-OSC_OUT\ (PH1).PinAttribute=CortexM7
PH1-OSC_OUT\ (PH1).Signal=RCC_OSC_OUT
PH2.GPIOParameters=GPIO_Speed,PinAttribute
PH2.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PH2.Locked=true
PH2.Mode=Dual_Bank
PH2.PinAttribute=CortexM7
PH2.Signal=QUADSPI_BK2_IO0
PH3.GPIOParameters=GPIO_Speed,PinAttribute
PH3.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PH3.Locked=true
PH3.Mode=Dual_Bank
PH3.PinAttribute=CortexM7
PH3.Signal=QUADSPI_BK2_IO1
PH5.GPIOParameters=PinAttribute
PH5.Locked=true
PH5.PinAttribute=CortexM7
PH5.Signal=FMC_SDNWE
PH6.GPIOParameters=PinAttribute
PH6.Locked=true
PH6.Mode=SdramChipSelect2_2
PH6.PinAttribute=CortexM7
PH6.Signal=FMC_SDNE1
PH7.GPIOParameters=PinAttribute
PH7.Locked=true
PH7.Mode=SdramChipSelect2_2
PH7.PinAttribute=CortexM7
PH7.Signal=FMC_SDCKE1
PH9.GPIOParameters=GPIO_Speed,PinAttribute
PH9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PH9.Locked=true
PH9.Mode=RGB565
PH9.PinAttribute=CortexM7
PH9.Signal=LTDC_R3
PI0.GPIOParameters=GPIO_Speed,PinAttribute
PI0.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PI0.Locked=true
PI0.Mode=RGB565
PI0.PinAttribute=CortexM7
PI0.Signal=LTDC_G5
PI1.GPIOParameters=GPIO_Speed,PinAttribute
PI1.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PI1.Locked=true
PI1.Mode=RGB565
PI1.PinAttribute=CortexM7
PI1.Signal=LTDC_G6
PI12.GPIOParameters=GPIO_Speed,PinAttribute
PI12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PI12.Locked=true
PI12.Mode=RGB565
PI12.PinAttribute=CortexM7
PI12.Signal=LTDC_HSYNC
PI14.GPIOParameters=GPIO_Speed,PinAttribute
PI14.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PI14.Locked=true
PI14.Mode=RGB565
PI14.PinAttribute=CortexM7
PI14.Signal=LTDC_CLK
PI15.GPIOParameters=GPIO_Speed,PinAttribute
PI15.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PI15.Locked=true
PI15.PinAttribute=CortexM7
PI15.Signal=LTDC_R0
PI9.GPIOParameters=GPIO_Speed,PinAttribute
PI9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PI9.Mode=RGB565
PI9.PinAttribute=CortexM7
PI9.Signal=LTDC_VSYNC
PJ0.GPIOParameters=GPIO_Speed,PinAttribute
PJ0.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ0.Locked=true
PJ0.PinAttribute=CortexM7
PJ0.Signal=LTDC_R1
PJ1.GPIOParameters=GPIO_Speed,PinAttribute
PJ1.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ1.Locked=true
PJ1.PinAttribute=CortexM7
PJ1.Signal=LTDC_R2
PJ10.GPIOParameters=GPIO_Speed,PinAttribute
PJ10.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ10.Locked=true
PJ10.Mode=RGB565
PJ10.PinAttribute=CortexM7
PJ10.Signal=LTDC_G3
PJ11.GPIOParameters=GPIO_Speed,PinAttribute
PJ11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ11.Locked=true
PJ11.Mode=RGB565
PJ11.PinAttribute=CortexM7
PJ11.Signal=LTDC_G4
PJ12.GPIOParameters=GPIO_Speed,PinAttribute
PJ12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ12.Locked=true
PJ12.PinAttribute=CortexM7
PJ12.Signal=LTDC_B0
PJ13.GPIOParameters=GPIO_Speed,PinAttribute
PJ13.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ13.Locked=true
PJ13.PinAttribute=CortexM7
PJ13.Signal=LTDC_B1
PJ14.GPIOParameters=GPIO_Speed,PinAttribute
PJ14.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ14.Locked=true
PJ14.PinAttribute=CortexM7
PJ14.Signal=LTDC_B2
PJ15.GPIOParameters=GPIO_Speed,PinAttribute
PJ15.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ15.Locked=true
PJ15.Mode=RGB565
PJ15.PinAttribute=CortexM7
PJ15.Signal=LTDC_B3
PJ3.GPIOParameters=GPIO_Speed,PinAttribute
PJ3.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ3.Locked=true
PJ3.Mode=RGB565
PJ3.PinAttribute=CortexM7
PJ3.Signal=LTDC_R4
PJ4.GPIOParameters=GPIO_Speed,PinAttribute
PJ4.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ4.Locked=true
PJ4.Mode=RGB565
PJ4.PinAttribute=CortexM7
PJ4.Signal=LTDC_R5
PJ5.GPIOParameters=GPIO_Speed,PinAttribute
PJ5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ5.Locked=true
PJ5.Mode=RGB565
PJ5.PinAttribute=CortexM7
PJ5.Signal=LTDC_R6
PJ6.GPIOParameters=GPIO_Speed,PinAttribute
PJ6.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ6.Locked=true
PJ6.Mode=RGB565
PJ6.PinAttribute=CortexM7
PJ6.Signal=LTDC_R7
PJ7.GPIOParameters=GPIO_Speed,PinAttribute
PJ7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ7.Locked=true
PJ7.PinAttribute=CortexM7
PJ7.Signal=LTDC_G0
PJ8.GPIOParameters=GPIO_Speed,PinAttribute
PJ8.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ8.Locked=true
PJ8.PinAttribute=CortexM7
PJ8.Signal=LTDC_G1
PJ9.GPIOParameters=GPIO_Speed,PinAttribute
PJ9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PJ9.Locked=true
PJ9.Mode=RGB565
PJ9.PinAttribute=CortexM7
PJ9.Signal=LTDC_G2
PK2.GPIOParameters=GPIO_Speed,PinAttribute
PK2.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PK2.Locked=true
PK2.Mode=RGB565
PK2.PinAttribute=CortexM7
PK2.Signal=LTDC_G7
PK3.GPIOParameters=GPIO_Speed,PinAttribute
PK3.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PK3.Locked=true
PK3.Mode=RGB565
PK3.PinAttribute=CortexM7
PK3.Signal=LTDC_B4
PK4.GPIOParameters=GPIO_Speed,PinAttribute
PK4.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PK4.Locked=true
PK4.Mode=RGB565
PK4.PinAttribute=CortexM7
PK4.Signal=LTDC_B5
PK5.GPIOParameters=GPIO_Speed,PinAttribute
PK5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PK5.Locked=true
PK5.Mode=RGB565
PK5.PinAttribute=CortexM7
PK5.Signal=LTDC_B6
PK6.GPIOParameters=GPIO_Speed,PinAttribute
PK6.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PK6.Locked=true
PK6.Mode=RGB565
PK6.PinAttribute=CortexM7
PK6.Signal=LTDC_B7
PK7.GPIOParameters=GPIO_Speed,PinAttribute
PK7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PK7.Mode=RGB565
PK7.PinAttribute=CortexM7
PK7.Signal=LTDC_DE
PinOutPanel.CurrentBGAView=Top
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.BootMode=boot0
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H745XIHx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.11.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=M4-0x200,M7-0x1000
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=STM32H745I_DISCO.ioc
ProjectManager.ProjectName=STM32H745I_DISCO
ProjectManager.ProjectStructure=M7\:CortexM7 Project\:true;M4\:CortexM4 Project\:true;
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=M4-0x400,M7-0x1000
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true-CortexM7,2-SystemClock_Config-RCC-false-HAL-false-CortexM7,3-MX_MDMA_Init-MDMA-false-HAL-true-CortexM7,4-MX_DMA_Init-DMA-false-HAL-true-CortexM7,5-MX_LTDC_Init-LTDC-false-HAL-true-CortexM7,6-MX_CRC_Init-CRC-false-HAL-true-CortexM7,7-MX_DMA2D_Init-DMA2D-false-HAL-true-CortexM7,8-MX_FREERTOS_Init-FREERTOS_M7-false-HAL-false-CortexM7,9-MX_JPEG_Init-JPEG-false-HAL-true-CortexM7,10-MX_QUADSPI_Init-QUADSPI-false-HAL-true-CortexM7,11-MX_ADC1_Init-ADC1-false-HAL-true-CortexM7,12-MX_FMC_Init-FMC-false-HAL-true-CortexM7,14-MX_TouchGFX_Init-STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7-false-HAL-false-CortexM7,15-MX_TouchGFX_Process-STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7-false-HAL-false-CortexM7,1-MX_MDMA_Init-MDMA-false-HAL-true-CortexM4,2-MX_DMA_Init-DMA-false-HAL-true-CortexM4,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true-CortexM7,0-MX_CORTEX_M4_Init-CORTEX_M4-false-HAL-true-CortexM4
QUADSPI.ChipSelectHighTime=QSPI_CS_HIGH_TIME_3_CYCLE
QUADSPI.ClockPrescaler=1
QUADSPI.FlashSize=26
QUADSPI.IPParameters=ChipSelectHighTime,FlashSize,SampleShifting,ClockPrescaler
QUADSPI.SampleShifting=QSPI_SAMPLE_SHIFTING_HALFCYCLE
RCC.ADCFreq_Value=50390625
RCC.AHB12Freq_Value=*********
RCC.AHB4Freq_Value=*********
RCC.APB1Freq_Value=100000000
RCC.APB2Freq_Value=100000000
RCC.APB3Freq_Value=100000000
RCC.APB4Freq_Value=100000000
RCC.AXIClockFreq_Value=*********
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CPU2Freq_Value=*********
RCC.CPU2SystikFreq_Value=*********
RCC.CortexFreq_Value=400000000
RCC.CpuClockFreq_Value=400000000
RCC.D1CPREFreq_Value=400000000
RCC.D1PPRE=RCC_APB3_DIV2
RCC.D2PPRE1=RCC_APB1_DIV2
RCC.D2PPRE2=RCC_APB2_DIV2
RCC.D3PPRE=RCC_APB4_DIV2
RCC.DFSDMACLkFreq_Value=*********
RCC.DFSDMFreq_Value=100000000
RCC.DIVM1=5
RCC.DIVM3=5
RCC.DIVN1=160
RCC.DIVN3=160
RCC.DIVP1Freq_Value=400000000
RCC.DIVP2Freq_Value=50390625
RCC.DIVP3Freq_Value=400000000
RCC.DIVQ1=4
RCC.DIVQ1Freq_Value=*********
RCC.DIVQ2Freq_Value=50390625
RCC.DIVQ3Freq_Value=400000000
RCC.DIVR1Freq_Value=400000000
RCC.DIVR2Freq_Value=50390625
RCC.DIVR3=83
RCC.DIVR3Freq_Value=9638554.21686747
RCC.FDCANFreq_Value=*********
RCC.FMCFreq_Value=*********
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=*********
RCC.HCLKFreq_Value=*********
RCC.HPRE=RCC_HCLK_DIV2
RCC.HRTIMFreq_Value=*********
RCC.I2C123Freq_Value=100000000
RCC.I2C4Freq_Value=100000000
RCC.IPParameters=ADCFreq_Value,AHB12Freq_Value,AHB4Freq_Value,APB1Freq_Value,APB2Freq_Value,APB3Freq_Value,APB4Freq_Value,AXIClockFreq_Value,CECFreq_Value,CKPERFreq_Value,CPU2Freq_Value,CPU2SystikFreq_Value,CortexFreq_Value,CpuClockFreq_Value,D1CPREFreq_Value,D1PPRE,D2PPRE1,D2PPRE2,D3PPRE,DFSDMACLkFreq_Value,DFSDMFreq_Value,DIVM1,DIVM3,DIVN1,DIVN3,DIVP1Freq_Value,DIVP2Freq_Value,DIVP3Freq_Value,DIVQ1,DIVQ1Freq_Value,DIVQ2Freq_Value,DIVQ3Freq_Value,DIVR1Freq_Value,DIVR2Freq_Value,DIVR3,DIVR3Freq_Value,FDCANFreq_Value,FMCFreq_Value,FamilyName,HCLK3ClockFreq_Value,HCLKFreq_Value,HPRE,HRTIMFreq_Value,I2C123Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM345Freq_Value,LPUART1Freq_Value,LTDCFreq_Value,MCO1PinFreq_Value,MCO2PinFreq_Value,PLLSourceVirtual,PWR_Regulator_Voltage_Scale,QSPIFreq_Value,RNGFreq_Value,RTCFreq_Value,SAI1Freq_Value,SAI23Freq_Value,SAI4AFreq_Value,SAI4BFreq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SPI123Freq_Value,SPI45Freq_Value,SPI6Freq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,Tim1OutputFreq_Value,Tim2OutputFreq_Value,TraceFreq_Value,USART16Freq_Value,USART234578Freq_Value,USBFreq_Value,VCO1OutputFreq_Value,VCO2OutputFreq_Value,VCO3OutputFreq_Value,VCOInput1Freq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value
RCC.LPTIM1Freq_Value=100000000
RCC.LPTIM2Freq_Value=100000000
RCC.LPTIM345Freq_Value=100000000
RCC.LPUART1Freq_Value=100000000
RCC.LTDCFreq_Value=9638554.21686747
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=400000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PWR_Regulator_Voltage_Scale=PWR_REGULATOR_VOLTAGE_SCALE1
RCC.QSPIFreq_Value=*********
RCC.RNGFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.SAI1Freq_Value=*********
RCC.SAI23Freq_Value=*********
RCC.SAI4AFreq_Value=*********
RCC.SAI4BFreq_Value=*********
RCC.SDMMCFreq_Value=*********
RCC.SPDIFRXFreq_Value=*********
RCC.SPI123Freq_Value=*********
RCC.SPI45Freq_Value=100000000
RCC.SPI6Freq_Value=100000000
RCC.SWPMI1Freq_Value=100000000
RCC.SYSCLKFreq_VALUE=400000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Tim1OutputFreq_Value=*********
RCC.Tim2OutputFreq_Value=*********
RCC.TraceFreq_Value=64000000
RCC.USART16Freq_Value=100000000
RCC.USART234578Freq_Value=100000000
RCC.USBFreq_Value=*********
RCC.VCO1OutputFreq_Value=*********
RCC.VCO2OutputFreq_Value=*********
RCC.VCO3OutputFreq_Value=*********
RCC.VCOInput1Freq_Value=5000000
RCC.VCOInput2Freq_Value=781250
RCC.VCOInput3Freq_Value=5000000
SH.ADCx_INP10.0=ADC1_INP10,IN10-Single-Ended
SH.ADCx_INP10.ConfNb=1
SH.FMC_A0.0=FMC_A0,12b-sda2
SH.FMC_A0.ConfNb=1
SH.FMC_A1.0=FMC_A1,12b-sda2
SH.FMC_A1.ConfNb=1
SH.FMC_A10.0=FMC_A10,12b-sda2
SH.FMC_A10.ConfNb=1
SH.FMC_A11.0=FMC_A11,12b-sda2
SH.FMC_A11.ConfNb=1
SH.FMC_A14_BA0.0=FMC_BA0,FourSdramBanks2
SH.FMC_A14_BA0.ConfNb=1
SH.FMC_A15_BA1.0=FMC_BA1,FourSdramBanks2
SH.FMC_A15_BA1.ConfNb=1
SH.FMC_A2.0=FMC_A2,12b-sda2
SH.FMC_A2.ConfNb=1
SH.FMC_A3.0=FMC_A3,12b-sda2
SH.FMC_A3.ConfNb=1
SH.FMC_A4.0=FMC_A4,12b-sda2
SH.FMC_A4.ConfNb=1
SH.FMC_A5.0=FMC_A5,12b-sda2
SH.FMC_A5.ConfNb=1
SH.FMC_A6.0=FMC_A6,12b-sda2
SH.FMC_A6.ConfNb=1
SH.FMC_A7.0=FMC_A7,12b-sda2
SH.FMC_A7.ConfNb=1
SH.FMC_A8.0=FMC_A8,12b-sda2
SH.FMC_A8.ConfNb=1
SH.FMC_A9.0=FMC_A9,12b-sda2
SH.FMC_A9.ConfNb=1
SH.FMC_D0_DA0.0=FMC_D0,sd-16b-d2
SH.FMC_D0_DA0.ConfNb=1
SH.FMC_D10_DA10.0=FMC_D10,sd-16b-d2
SH.FMC_D10_DA10.ConfNb=1
SH.FMC_D11_DA11.0=FMC_D11,sd-16b-d2
SH.FMC_D11_DA11.ConfNb=1
SH.FMC_D12_DA12.0=FMC_D12,sd-16b-d2
SH.FMC_D12_DA12.ConfNb=1
SH.FMC_D13_DA13.0=FMC_D13,sd-16b-d2
SH.FMC_D13_DA13.ConfNb=1
SH.FMC_D14_DA14.0=FMC_D14,sd-16b-d2
SH.FMC_D14_DA14.ConfNb=1
SH.FMC_D15_DA15.0=FMC_D15,sd-16b-d2
SH.FMC_D15_DA15.ConfNb=1
SH.FMC_D1_DA1.0=FMC_D1,sd-16b-d2
SH.FMC_D1_DA1.ConfNb=1
SH.FMC_D2_DA2.0=FMC_D2,sd-16b-d2
SH.FMC_D2_DA2.ConfNb=1
SH.FMC_D3_DA3.0=FMC_D3,sd-16b-d2
SH.FMC_D3_DA3.ConfNb=1
SH.FMC_D4_DA4.0=FMC_D4,sd-16b-d2
SH.FMC_D4_DA4.ConfNb=1
SH.FMC_D5_DA5.0=FMC_D5,sd-16b-d2
SH.FMC_D5_DA5.ConfNb=1
SH.FMC_D6_DA6.0=FMC_D6,sd-16b-d2
SH.FMC_D6_DA6.ConfNb=1
SH.FMC_D7_DA7.0=FMC_D7,sd-16b-d2
SH.FMC_D7_DA7.ConfNb=1
SH.FMC_D8_DA8.0=FMC_D8,sd-16b-d2
SH.FMC_D8_DA8.ConfNb=1
SH.FMC_D9_DA9.0=FMC_D9,sd-16b-d2
SH.FMC_D9_DA9.ConfNb=1
SH.FMC_NBL0.0=FMC_NBL0,Sd2ByteEnable2
SH.FMC_NBL0.ConfNb=1
SH.FMC_NBL1.0=FMC_NBL1,Sd2ByteEnable2
SH.FMC_NBL1.ConfNb=1
SH.FMC_SDCLK.0=FMC_SDCLK,12b-sda2
SH.FMC_SDCLK.ConfNb=1
SH.FMC_SDNCAS.0=FMC_SDNCAS,12b-sda2
SH.FMC_SDNCAS.ConfNb=1
SH.FMC_SDNRAS.0=FMC_SDNRAS,12b-sda2
SH.FMC_SDNRAS.ConfNb=1
SH.FMC_SDNWE.0=FMC_SDNWE,12b-sda2
SH.FMC_SDNWE.ConfNb=1
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_IsPackSelfContextualization=true
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.ApplicationCcGraphicsJjApplication=TouchGFXOoGenerator
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.GraphicsJjApplication_Checked=true
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.IPParameters=ApplicationCcGraphicsJjApplication,tgfx_custom_height,tgfx_location,tgfx_address1,tgfx_address2,tgfx_display_interface,tgfx_vsync,tgfx_datareader,tgfx_hardware_accelerator,tgfx_buffering_strategy,tgfx_vector_rendering,tgfx_video,tgfx_video_width,tgfx_video_height,tgfx_video_strategy,tgfx_vector_fonts
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_address1=0xD0000000
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_address2=0xD0200000
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_buffering_strategy=Double
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_custom_height=272
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_datareader=Disabled
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_display_interface=disp_ltdc
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_hardware_accelerator=dma_2d
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_location=Allocate
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_vector_fonts=Enabled
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_vector_rendering=Software
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_video=Hardware
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_video_height=288
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_video_strategy=0
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_video_width=480
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7.tgfx_vsync=vsync_ltdc
STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0_M7_SwParameter=ApplicationCcGraphicsJjApplication\:TouchGFXOoGenerator;
SYS.userName=SYS_M7
VP_CRC_VS_CRC.Mode=CRC_Activate
VP_CRC_VS_CRC.Signal=CRC_VS_CRC
VP_DMA2D_VS_DMA2D.Mode=DMA2D_Activate
VP_DMA2D_VS_DMA2D.Signal=DMA2D_VS_DMA2D
VP_FREERTOS_M7_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_M7_VS_CMSIS_V2.Signal=FREERTOS_M7_VS_CMSIS_V2
VP_JPEG_VS_JPEG.Mode=JPEG_Activate
VP_JPEG_VS_JPEG.Signal=JPEG_VS_JPEG
VP_STMicroelectronics.X-CUBE-TOUCHGFX_M7_VS_GraphicsJjApplication_4.25.0.Mode=GraphicsJjApplication
VP_STMicroelectronics.X-CUBE-TOUCHGFX_M7_VS_GraphicsJjApplication_4.25.0.Signal=STMicroelectronics.X-CUBE-TOUCHGFX_M7_VS_GraphicsJjApplication_4.25.0
VP_SYS_M4_VS_Systick.Mode=SysTick
VP_SYS_M4_VS_Systick.Signal=SYS_M4_VS_Systick
VP_SYS_VS_tim6.Mode=TIM6
VP_SYS_VS_tim6.Signal=SYS_VS_tim6
board=custom
rtos.0.ip=FREERTOS_M7
rtos.0.tasks.0=allocationType,Dynamic;bufferName,NULL;codeGen,Default;controlBlockName,NULL;entry,StartDefaultTask;name,defaultTask;parameter,NULL;priority,osPriorityNormal;stackSize,128
