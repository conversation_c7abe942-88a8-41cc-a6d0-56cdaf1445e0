# STM32H745I TouchGFX Comprehensive Compilation Script
Write-Host "Starting comprehensive STM32H745I TouchGFX project compilation..." -ForegroundColor Cyan

# Set environment variables
$env:PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin;" + $env:PATH

# Define tools
$GCC = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe"
$GPP = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-g++.exe"

# Common compiler flags
$COMMON_FLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb", 
    "-mfpu=fpv5-sp-d16",
    "-mfloat-abi=hard",
    "-DUSE_HAL_DRIVER",
    "-DSTM32H745xx", 
    "-DCORE_CM7",
    "-DUSE_PWR_DIRECT_SMPS_SUPPLY",
    "-DST",
    "-DUSE_STM32H745I_DISCO",
    "-DUSE_BPP=16"
)

# Include paths - 添加所有必要的路径
$TOUCHGFX_PATH = "C:\Users\<USER>\STMicroelectronics\TouchGFX\4.25.0\touchgfx\framework\include"
$INCLUDE_PATHS = @(
    "-ICM7/Core/Inc",
    "-ICM7/TouchGFX/App",
    "-ICM7/TouchGFX/target/generated", 
    "-ICM7/TouchGFX/target",
    "-ICM7/TouchGFX/gui/include",
    "-ICM7/TouchGFX/generated/gui_generated/include",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc",
    "-IDrivers/STM32H7xx_HAL_Driver/Inc/Legacy",
    "-IDrivers/CMSIS/Device/ST/STM32H7xx/Include",
    "-IDrivers/CMSIS/Include",
    "-IDrivers/BSP/STM32H745I-DISCO",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/include",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2",
    "-IMiddlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F",
    "-I$TOUCHGFX_PATH"
)

# C compiler flags
$CFLAGS = $COMMON_FLAGS + @(
    "--specs=nano.specs",
    "-O0",
    "-Wall",
    "-fdata-sections", 
    "-ffunction-sections",
    "-g3",
    "-std=gnu11"
)

# C++ compiler flags  
$CPPFLAGS = $COMMON_FLAGS + @(
    "--specs=nano.specs",
    "-O0", 
    "-fdata-sections",
    "-fno-exceptions",
    "-fno-rtti",
    "-fno-use-cxa-atexit",
    "-Wall",
    "-g3",
    "-std=c++17"
)

# Create output directory
$OUTPUT_DIR = "build_comprehensive"
if (Test-Path $OUTPUT_DIR) {
    Remove-Item -Recurse -Force $OUTPUT_DIR
}
New-Item -ItemType Directory -Path $OUTPUT_DIR | Out-Null

Write-Host "Starting comprehensive compilation..." -ForegroundColor Yellow

# Function to compile with error handling
function Compile-File {
    param($Compiler, $Flags, $SourceFile, $OutputFile, $FileType)
    
    if (!(Test-Path $SourceFile)) {
        Write-Host "⚠️ File not found: $SourceFile" -ForegroundColor Yellow
        return $false
    }
    
    Write-Host "Compiling $FileType`: $SourceFile" -ForegroundColor White
    $cmd_args = $Flags + $INCLUDE_PATHS + @("-c", $SourceFile, "-o", "$OUTPUT_DIR/$OutputFile")
    
    try {
        & $Compiler @cmd_args 2>&1 | Tee-Object -Variable output
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Success: $OutputFile" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed: $SourceFile" -ForegroundColor Red
            Write-Host "Error output:" -ForegroundColor Red
            $output | Write-Host -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Exception compiling $SourceFile`: $_" -ForegroundColor Red
        return $false
    }
}

$SuccessCount = 0
$ErrorCount = 0

# Core C files
$CoreCFiles = @(
    @("CM7/Core/Src/main.c", "main.o"),
    @("CM7/Core/Src/stm32h7xx_it.c", "stm32h7xx_it.o"),
    @("CM7/Core/Src/stm32h7xx_hal_msp.c", "stm32h7xx_hal_msp.o"),
    @("CM7/Core/Src/stm32h7xx_hal_timebase_tim.c", "stm32h7xx_hal_timebase_tim.o"),
    @("CM7/Core/Src/freertos.c", "freertos.o"),
    @("CM7/Core/Src/main_user.c", "main_user.o")
)

Write-Host "`n=== Compiling Core C Files ===" -ForegroundColor Cyan
foreach ($file in $CoreCFiles) {
    if (Compile-File $GCC $CFLAGS $file[0] $file[1] "C") {
        $SuccessCount++
    } else {
        $ErrorCount++
    }
}

# TouchGFX App C files
$TouchGFXCFiles = @(
    @("CM7/TouchGFX/App/app_touchgfx.c", "app_touchgfx.o")
)

Write-Host "`n=== Compiling TouchGFX App C Files ===" -ForegroundColor Cyan
foreach ($file in $TouchGFXCFiles) {
    if (Compile-File $GCC $CFLAGS $file[0] $file[1] "C") {
        $SuccessCount++
    } else {
        $ErrorCount++
    }
}

# TouchGFX C++ files
$TouchGFXCppFiles = @(
    @("CM7/TouchGFX/gui/src/model/Model.cpp", "Model.o"),
    @("CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp", "Screen1Presenter.o"),
    @("CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp", "Screen1View.o")
)

Write-Host "`n=== Compiling TouchGFX C++ Files ===" -ForegroundColor Cyan
foreach ($file in $TouchGFXCppFiles) {
    if (Compile-File $GPP $CPPFLAGS $file[0] $file[1] "C++") {
        $SuccessCount++
    } else {
        $ErrorCount++
    }
}

# TouchGFX Target C++ files
$TouchGFXTargetFiles = @(
    @("CM7/TouchGFX/target/TouchGFXHAL.cpp", "TouchGFXHAL.o"),
    @("CM7/TouchGFX/target/TouchGFXGPIO.cpp", "TouchGFXGPIO.o"),
    @("CM7/TouchGFX/target/STM32TouchController.cpp", "STM32TouchController.o"),
    @("CM7/TouchGFX/target/STM32H7Instrumentation.cpp", "STM32H7Instrumentation.o")
)

Write-Host "`n=== Compiling TouchGFX Target C++ Files ===" -ForegroundColor Cyan
foreach ($file in $TouchGFXTargetFiles) {
    if (Compile-File $GPP $CPPFLAGS $file[0] $file[1] "C++") {
        $SuccessCount++
    } else {
        $ErrorCount++
    }
}

Write-Host "`n=== Compilation Summary ===" -ForegroundColor Cyan
Write-Host "✅ Successful: $SuccessCount files" -ForegroundColor Green
Write-Host "❌ Failed: $ErrorCount files" -ForegroundColor Red

if ($ErrorCount -eq 0) {
    Write-Host "🎉 All files compiled successfully!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Some files failed to compile. Check errors above." -ForegroundColor Yellow
}

# List generated object files
if (Test-Path $OUTPUT_DIR) {
    Write-Host "`nGenerated object files:" -ForegroundColor Cyan
    Get-ChildItem $OUTPUT_DIR -Filter "*.o" | ForEach-Object {
        $size = [math]::Round($_.Length / 1KB, 2)
        Write-Host "  $($_.Name) ($size KB)" -ForegroundColor White
    }
}
