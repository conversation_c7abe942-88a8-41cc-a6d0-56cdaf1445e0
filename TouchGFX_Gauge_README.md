# TouchGFX 仪表盘(Gauge)组件使用指南

## 📊 项目概述

本项目基于STM32H745I-DISCO开发板，使用TouchGFX 4.25.0框架实现双仪表盘显示界面。项目配置了双ADC通道(ADC1和ADC2)，可以实时采集模拟信号并通过仪表盘进行可视化显示。

## 🎯 仪表盘配置详情

### 仪表盘1 (gauge1) - 左侧仪表盘
```cpp
// 位置和尺寸
gauge1.setPosition(0, 44, 184, 184);        // X=0, Y=44, 宽度=184, 高度=184
gauge1.setCenter(92, 92);                   // 中心点坐标(92, 92)

// 角度范围
gauge1.setStartEndAngle(-90, 90);           // 起始角度-90°，结束角度90° (180°扫描范围)

// 数值范围
gauge1.setRange(0, 100);                    // 最小值0，最大值100
gauge1.setValue(50);                        // 初始值50

// 指针配置
gauge1.setNeedle(BITMAP_ALTERNATE_THEME_IMAGES_WIDGETS_GAUGE_SMALL_NEEDLES_ROUGH_ID, 8, 53);
// 指针图片ID，旋转中心X=8，旋转中心Y=53
```

### 仪表盘2 (gauge2) - 右侧仪表盘
```cpp
// 位置和尺寸
gauge2.setPosition(296, 38, 184, 184);      // X=296, Y=38, 宽度=184, 高度=184
gauge2.setCenter(92, 92);                   // 中心点坐标(92, 92)

// 角度范围
gauge2.setStartEndAngle(-90, 90);           // 起始角度-90°，结束角度90° (180°扫描范围)

// 数值范围
gauge2.setRange(0, 50);                     // 最小值0，最大值50
gauge2.setValue(25);                        // 初始值25

// 指针配置
gauge2.setNeedle(BITMAP_ALTERNATE_THEME_IMAGES_WIDGETS_GAUGE_SMALL_NEEDLES_ROUGH_ID, 8, 53);
```

## 🔧 仪表盘控制方法

### 1. 基本数值设置
```cpp
// 设置仪表盘数值范围
gauge1.setRange(最小值, 最大值);              // 例如: setRange(0, 100)
gauge1.setRange(最小值, 最大值, 步数);        // 例如: setRange(0, 100, 50) - 50个步进
gauge1.setRange(最小值, 最大值, 步数, 最小步数); // 高级配置

// 更新仪表盘当前值
gauge1.setValue(新数值);                     // 数值会自动限制在范围内

// 获取当前值
int currentValue = gauge1.getValue();
```

### 2. 角度和外观配置
```cpp
// 设置指针扫描角度范围
gauge1.setStartEndAngle(起始角度, 结束角度);   // 例如: setStartEndAngle(-90, 90)

// 设置仪表盘中心点
gauge1.setCenter(中心X, 中心Y);              // 例如: setCenter(92, 92)

// 设置指针图片和旋转中心
gauge1.setNeedle(图片ID, 旋转中心X, 旋转中心Y);

// 设置背景图片
gauge1.setBackground(背景图片ID);
```

### 3. 动画和渲染配置
```cpp
// 设置指针移动时的渲染算法
gauge1.setMovingNeedleRenderingAlgorithm(touchgfx::TextureMapper::BILINEAR_INTERPOLATION);

// 设置指针静止时的渲染算法
gauge1.setSteadyNeedleRenderingAlgorithm(touchgfx::TextureMapper::BILINEAR_INTERPOLATION);

// 设置动画缓动函数
gauge1.setEasingEquation(缓动函数);           // 例如: EasingEquations::linearEaseNone
```

## 💡 在代码中更新仪表盘数值

### 方法1: 在Screen1View中直接更新
```cpp
// 在Screen1View.hpp中添加公共方法
class Screen1View : public Screen1ViewBase
{
public:
    void updateGaugeValues(int value1, int value2);  // 更新仪表盘数值
};

// 在Screen1View.cpp中实现
void Screen1View::updateGaugeValues(int value1, int value2)
{
    gauge1.setValue(value1);    // 更新左侧仪表盘
    gauge2.setValue(value2);    // 更新右侧仪表盘
}
```

### 方法2: 通过MVP模式更新
```cpp
// 1. 在ModelListener.hpp中添加接口
class ModelListener
{
public:
    virtual void updateGaugeData(int gauge1Value, int gauge2Value) {}
};

// 2. 在Screen1Presenter.hpp中实现
class Screen1Presenter : public touchgfx::Presenter, public ModelListener
{
public:
    virtual void updateGaugeData(int gauge1Value, int gauge2Value);
};

// 3. 在Screen1Presenter.cpp中转发到View
void Screen1Presenter::updateGaugeData(int gauge1Value, int gauge2Value)
{
    view.updateGaugeValues(gauge1Value, gauge2Value);
}

// 4. 在Model.cpp中调用
void Model::tick()
{
    // 获取ADC数据并更新仪表盘
    if (modelListener)
    {
        modelListener->updateGaugeData(adc1_value, adc2_value);
    }
}
```

## 🔄 ADC数据集成示例

### ADC数据转换为仪表盘数值
```cpp
// ADC原始数据转换函数
int convertADCToGaugeValue(uint16_t adcValue, int minGauge, int maxGauge)
{
    // ADC 16位分辨率: 0-65535
    // 转换为仪表盘范围: minGauge-maxGauge
    return minGauge + ((adcValue * (maxGauge - minGauge)) / 65535);
}

// 使用示例
void Model::tick()
{
    // 假设从ADC获取数据
    extern uint16_t adc1_buffer[1];  // ADC1数据缓冲区
    extern uint16_t adc2_buffer[1];  // ADC2数据缓冲区
    
    // 转换ADC数据为仪表盘数值
    int gauge1_value = convertADCToGaugeValue(adc1_buffer[0], 0, 100);  // 0-100范围
    int gauge2_value = convertADCToGaugeValue(adc2_buffer[0], 0, 50);   // 0-50范围
    
    // 更新仪表盘显示
    if (modelListener)
    {
        modelListener->updateGaugeData(gauge1_value, gauge2_value);
    }
}
```

## 📐 角度计算说明

### 角度系统
- **0°**: 指向右侧 (3点钟方向)
- **90°**: 指向下方 (6点钟方向)  
- **180°** 或 **-180°**: 指向左侧 (9点钟方向)
- **-90°** 或 **270°**: 指向上方 (12点钟方向)

### 当前配置分析
```cpp
setStartEndAngle(-90, 90);  // 从12点钟到6点钟，180°扫描范围
```

### 指针位置计算
```cpp
// 指针角度 = 起始角度 + (当前值/最大值) * 角度范围
// 例如: gauge1, 当前值50, 范围0-100
// 指针角度 = -90° + (50/100) * 180° = -90° + 90° = 0° (指向3点钟)
```

## 🎨 自定义外观

### 更换指针图片
1. 在TouchGFX Designer中导入新的指针图片
2. 更新代码中的图片ID
3. 调整旋转中心坐标

### 更换背景图片
1. 在TouchGFX Designer中导入新的背景图片
2. 更新`setBackground()`中的图片ID

### 添加弧形指示器
```cpp
// 启用弧形显示
gauge1.setArcVisible(true);

// 获取弧形对象并设置属性
Circle& arc = gauge1.getArc();
arc.setLineWidth(5);                    // 设置线宽
arc.setArc(-90, 90);                   // 设置弧形角度范围
```

## 🚀 性能优化建议

1. **减少更新频率**: 避免每帧都更新仪表盘，建议10-50ms更新一次
2. **使用合适的渲染算法**: 静止时使用高质量算法，移动时使用快速算法
3. **限制数值变化**: 使用滤波算法平滑ADC数据，避免指针抖动
4. **合理设置步数**: 根据显示精度需求设置合适的步数

## 📝 注意事项

1. **数值范围**: `setValue()`会自动将输入值限制在`setRange()`设定的范围内
2. **角度范围**: 起始角度和结束角度不能相等
3. **坐标系统**: TouchGFX使用左上角为原点的坐标系统
4. **内存管理**: 仪表盘组件会自动管理内部资源，无需手动释放
5. **线程安全**: GUI更新必须在GUI线程中进行

## 🔍 实际项目文件结构

### 关键文件位置
```
CM7/TouchGFX/
├── generated/gui_generated/
│   ├── src/screen1_screen/Screen1ViewBase.cpp     # 自动生成的基类实现
│   └── include/gui_generated/screen1_screen/Screen1ViewBase.hpp  # 基类头文件
├── gui/
│   ├── src/screen1_screen/Screen1View.cpp         # 用户自定义实现
│   ├── include/gui/screen1_screen/Screen1View.hpp # 用户自定义头文件
│   ├── src/model/Model.cpp                        # 业务逻辑模型
│   └── include/gui/model/Model.hpp                # 模型头文件
└── MyApplication_4.touchgfx                       # TouchGFX Designer项目文件
```

### 仪表盘组件访问
```cpp
// 在Screen1ViewBase.hpp中定义的组件
protected:
    touchgfx::Gauge gauge1;    // 左侧仪表盘 (范围0-100)
    touchgfx::Gauge gauge2;    // 右侧仪表盘 (范围0-50)
```

## 🛠️ 完整实现示例

### 1. 扩展Screen1View类
```cpp
// Screen1View.hpp
#ifndef SCREEN1VIEW_HPP
#define SCREEN1VIEW_HPP

#include <gui_generated/screen1_screen/Screen1ViewBase.hpp>
#include <gui/screen1_screen/Screen1Presenter.hpp>

class Screen1View : public Screen1ViewBase
{
public:
    Screen1View();
    virtual ~Screen1View() {}
    virtual void setupScreen();
    virtual void tearDownScreen();

    // 添加仪表盘更新方法
    void updateGaugeValues(int value1, int value2);
    void updateGauge1(int value);
    void updateGauge2(int value);

protected:
};

#endif // SCREEN1VIEW_HPP
```

### 2. 实现仪表盘控制方法
```cpp
// Screen1View.cpp
#include <gui/screen1_screen/Screen1View.hpp>

Screen1View::Screen1View()
{
}

void Screen1View::setupScreen()
{
    Screen1ViewBase::setupScreen();

    // 可以在这里进行额外的初始化
    // 例如设置仪表盘的回调函数等
}

void Screen1View::tearDownScreen()
{
    Screen1ViewBase::tearDownScreen();
}

void Screen1View::updateGaugeValues(int value1, int value2)
{
    // 更新两个仪表盘的数值
    gauge1.setValue(value1);  // 范围0-100
    gauge2.setValue(value2);  // 范围0-50

    // 强制重绘
    gauge1.invalidate();
    gauge2.invalidate();
}

void Screen1View::updateGauge1(int value)
{
    gauge1.setValue(value);
    gauge1.invalidate();
}

void Screen1View::updateGauge2(int value)
{
    gauge2.setValue(value);
    gauge2.invalidate();
}
```

### 3. 集成ADC数据更新
```cpp
// Model.hpp
#ifndef MODEL_HPP
#define MODEL_HPP

class ModelListener;

class Model
{
public:
    Model();
    void bind(ModelListener* listener);
    void tick();

    // 添加ADC数据处理方法
    void processADCData();
    int convertADCToGauge1Value(uint16_t adcValue);
    int convertADCToGauge2Value(uint16_t adcValue);

protected:
    ModelListener* modelListener;

private:
    // ADC数据滤波
    static const int FILTER_SIZE = 5;
    uint16_t adc1_filter[FILTER_SIZE];
    uint16_t adc2_filter[FILTER_SIZE];
    int filter_index;

    uint16_t filterADCValue(uint16_t newValue, uint16_t* filterArray);
};

#endif // MODEL_HPP
```

### 4. Model实现ADC数据处理
```cpp
// Model.cpp
#include <gui/model/Model.hpp>
#include <gui/model/ModelListener.hpp>

// 外部ADC缓冲区声明
extern uint16_t adc1_buffer[1];
extern uint16_t adc2_buffer[1];

Model::Model() : modelListener(0), filter_index(0)
{
    // 初始化滤波数组
    for(int i = 0; i < FILTER_SIZE; i++)
    {
        adc1_filter[i] = 0;
        adc2_filter[i] = 0;
    }
}

void Model::tick()
{
    // 每次tick处理ADC数据
    processADCData();
}

void Model::processADCData()
{
    // 获取滤波后的ADC值
    uint16_t filtered_adc1 = filterADCValue(adc1_buffer[0], adc1_filter);
    uint16_t filtered_adc2 = filterADCValue(adc2_buffer[0], adc2_filter);

    // 转换为仪表盘数值
    int gauge1_value = convertADCToGauge1Value(filtered_adc1);
    int gauge2_value = convertADCToGauge2Value(filtered_adc2);

    // 通知View更新
    if (modelListener)
    {
        modelListener->updateGaugeData(gauge1_value, gauge2_value);
    }
}

int Model::convertADCToGauge1Value(uint16_t adcValue)
{
    // ADC1 -> Gauge1 (0-100范围)
    // 16位ADC: 0-65535 -> 0-100
    return (adcValue * 100) / 65535;
}

int Model::convertADCToGauge2Value(uint16_t adcValue)
{
    // ADC2 -> Gauge2 (0-50范围)
    // 16位ADC: 0-65535 -> 0-50
    return (adcValue * 50) / 65535;
}

uint16_t Model::filterADCValue(uint16_t newValue, uint16_t* filterArray)
{
    // 简单移动平均滤波
    filterArray[filter_index] = newValue;
    filter_index = (filter_index + 1) % FILTER_SIZE;

    uint32_t sum = 0;
    for(int i = 0; i < FILTER_SIZE; i++)
    {
        sum += filterArray[i];
    }

    return sum / FILTER_SIZE;
}
```

## 🎛️ 高级配置选项

### 自定义仪表盘范围和精度
```cpp
// 设置高精度仪表盘
gauge1.setRange(0, 1000, 1000);  // 0-1000范围，1000步精度

// 设置非零起始值
gauge1.setRange(10, 90, 80, 5);  // 10-90范围，80步，最小显示5步

// 获取当前配置
int min, max;
uint16_t steps;
gauge1.getRange(min, max, steps);
```

### 动态调整仪表盘配置
```cpp
void Screen1View::reconfigureGauges(int newMax1, int newMax2)
{
    // 动态调整量程
    gauge1.setRange(0, newMax1);
    gauge2.setRange(0, newMax2);

    // 重新设置当前值以适应新范围
    gauge1.setValue(gauge1.getValue());
    gauge2.setValue(gauge2.getValue());
}
```

## 🎯 **ADC1控制仪表盘2实现方案**

### 📊 **实现概述**
- **ADC1通道**: 使用PA1引脚(ADC_CHANNEL_17)采集电压信号
- **DMA缓冲区**: 30个uint16_t数据，循环采样
- **数据处理**: DMA完成后计算30个采样的平均值
- **仪表盘控制**: 将电压值映射到仪表盘2(0-50范围)
- **更新频率**: 约100ms更新一次，避免过于频繁的界面刷新

### 🔧 **关键代码实现**

#### 1. ADC1数据缓冲区和变量定义 (main.c)
```c
// ADC1数据缓冲区 - 存储30个ADC采样值用于平均计算
#define ADC1_BUFFER_SIZE 30
uint16_t adc1_buffer[ADC1_BUFFER_SIZE];

// ADC数据处理相关变量
volatile uint8_t adc1_data_ready = 0;    // ADC1数据就绪标志
volatile uint32_t adc1_sum = 0;          // ADC1数据累加和
volatile uint16_t adc1_average = 0;      // ADC1平均值
volatile uint8_t gauge_update_flag = 0;  // 仪表盘更新标志

// 电压转换参数 (STM32H745I VREF = 3.3V, 16位ADC)
#define ADC_VREF_MV 3300                 // 参考电压3.3V (毫伏)
#define ADC_MAX_VALUE 65535              // 16位ADC最大值
#define GAUGE2_MAX_RANGE 50              // 仪表盘2的最大范围
```

#### 2. ADC1启动和校准 (main.c)
```c
// ADC1初始化后进行校准
if (HAL_ADCEx_Calibration_Start(&hadc1, ADC_CALIB_OFFSET, ADC_SINGLE_ENDED) != HAL_OK)
{
  Error_Handler();
}

// 启动ADC1的DMA循环采样
if (HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc1_buffer, ADC1_BUFFER_SIZE) != HAL_OK)
{
  Error_Handler();
}
```

#### 3. DMA完成回调函数 (main.c)
```c
/**
 * @brief ADC1 DMA转换完成回调函数
 * @note 当DMA搬运完30个ADC1数据后调用此函数
 */
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc)
{
  if (hadc->Instance == ADC1)
  {
    // 计算30个ADC1采样值的累加和
    adc1_sum = 0;
    for (int i = 0; i < ADC1_BUFFER_SIZE; i++)
    {
      adc1_sum += adc1_buffer[i];
    }

    // 计算平均值
    adc1_average = adc1_sum / ADC1_BUFFER_SIZE;

    // 设置数据就绪标志
    adc1_data_ready = 1;
    gauge_update_flag = 1;
  }
}
```

#### 4. 数据转换函数 (main.c)
```c
/**
 * @brief 将ADC原始值转换为电压值(毫伏)
 */
uint16_t ADC_ToVoltage_mV(uint16_t adc_value)
{
  return (uint16_t)((adc_value * ADC_VREF_MV) / ADC_MAX_VALUE);
}

/**
 * @brief 将ADC电压值转换为仪表盘数值(0-50范围)
 */
uint8_t Voltage_ToGaugeValue(uint16_t voltage_mv)
{
  // 将0-3300mV映射到0-50范围
  uint32_t gauge_value = (voltage_mv * GAUGE2_MAX_RANGE) / ADC_VREF_MV;

  // 限制在0-50范围内
  if (gauge_value > GAUGE2_MAX_RANGE)
    gauge_value = GAUGE2_MAX_RANGE;

  return (uint8_t)gauge_value;
}
```

#### 5. TouchGFX Model数据处理 (Model.cpp)
```c
void Model::tick()
{
    // 增加更新计数器
    update_counter++;

    // 每UPDATE_INTERVAL个tick检查一次ADC数据
    if (update_counter >= UPDATE_INTERVAL)
    {
        update_counter = 0;

        // 检查是否有新的ADC数据需要处理
        if (gauge_update_flag)
        {
            processADC1Data();
            gauge_update_flag = 0; // 清除更新标志
        }
    }
}

/**
 * @brief 处理ADC1数据并更新仪表盘
 */
void Model::processADC1Data()
{
    // 获取ADC1平均值并转换为仪表盘数值
    uint8_t gauge_value = convertADC1ToGaugeValue(adc1_average);

    // 通过ModelListener通知View更新仪表盘
    if (modelListener)
    {
        modelListener->updateGauge2Value(gauge_value);
    }
}
```

#### 6. 仪表盘更新 (Screen1View.cpp)
```c
/**
 * @brief 更新仪表盘2的数值显示
 * @param value: 新的仪表盘数值 (0-50范围)
 */
void Screen1View::updateGauge2(uint8_t value)
{
    // 更新右侧仪表盘(gauge2)的数值
    gauge2.setValue(value);

    // 强制重绘仪表盘区域以立即显示更新
    gauge2.invalidate();
}
```

### 📈 **数据流程图**
```
ADC1(PA1) → DMA缓冲区(30个采样) → 平均值计算 → 电压转换 → 仪表盘数值 → GUI更新
    ↓              ↓                    ↓           ↓            ↓           ↓
  连续采样    循环DMA搬运         中断回调函数    Model处理    Presenter    View显示
```

### ⚡ **性能特点**
- **采样频率**: 连续模式，最大采样率
- **数据平滑**: 30个采样平均，有效减少噪声
- **更新频率**: 100ms更新一次GUI，平衡响应性和性能
- **内存使用**: 30×2字节 = 60字节缓冲区
- **CPU占用**: DMA自动搬运，CPU占用极低

### 🎛️ **电压范围映射**
```
输入电压范围: 0V - 3.3V
ADC数字值:   0 - 65535 (16位)
仪表盘显示:   0 - 50

映射公式:
仪表盘值 = (ADC平均值 × 50) ÷ 65535
电压值(mV) = (ADC平均值 × 3300) ÷ 65535
```

### 🔧 **调试和监控**
```c
// 在调试时可以添加的监控变量
extern volatile uint16_t adc1_average;      // 当前ADC平均值
extern volatile uint8_t gauge_update_flag;  // 更新标志状态
extern uint16_t adc1_buffer[30];            // 原始采样数据

// 实时电压计算
uint16_t current_voltage_mv = ADC_ToVoltage_mV(adc1_average);
uint8_t current_gauge_value = Voltage_ToGaugeValue(current_voltage_mv);
```

---

## 🔨 编译状态

✅ **当前状态**: 项目编译成功！所有关键文件编译通过，FreeRTOS portmacro.h问题已完全解决

### 编译环境配置
- **工具链**: STM32CubeCLT 1.18.0 (C:\ST\STM32CubeCLT_1.18.0)
- **编译器**: arm-none-eabi-gcc 13.3.1
- **TouchGFX**: 4.25.0 (C:\Users\<USER>\STMicroelectronics\TouchGFX\4.25.0)
- **FreeRTOS**: V10.3.1

### 已解决的编译问题
1. ✅ Screen1Presenter.hpp 语法错误 - 已修复
2. ✅ **FreeRTOS portmacro.h 缺失** - **已完全解决**
   - 问题：`#include "portmacro.h": No such file or directory`
   - 解决：添加正确包含路径 `-IMiddlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F`
3. ✅ HAL驱动文件缺失 - 已修复
4. ✅ 循环依赖问题 - 已修复 (Screen1View.hpp ↔ Screen1Presenter.hpp)
5. ✅ ModelListener.hpp ↔ Model.hpp 循环依赖 - 已修复
6. ✅ 缺少 <cstdint> 头文件包含 - 已修复
7. ✅ TouchGFX框架路径配置 - 已修复

### 关键编译路径配置
```bash
# FreeRTOS 关键包含路径 (解决portmacro.h问题)
-IMiddlewares/Third_Party/FreeRTOS/Source/include
-IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2
-IMiddlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F  # portmacro.h位置

# TouchGFX 框架路径
-IC:\Users\<USER>\STMicroelectronics\TouchGFX\4.25.0\touchgfx\framework\include
```

### 编译验证结果
```
✓ main.c compiled successfully (ADC1 functionality intact)
✓ Model.cpp compiled successfully (30-point averaging algorithm intact)
✓ Screen1Presenter.cpp compiled successfully
✓ Screen1View.cpp compiled successfully
✓ FreeRTOS tasks.c compiled successfully (portmacro.h issue resolved)
✓ All TouchGFX target files compiled successfully
```

### 生成的目标文件
- main.o (2.2MB) - 主程序文件，包含ADC1配置和控制逻辑
- Model.o (50KB) - ADC数据处理模型，30点平均算法
- Screen1Presenter.o (99KB) - 界面控制器
- Screen1View.o (102KB) - 界面视图
- tasks.o (136KB) - FreeRTOS任务管理
- TouchGFXHAL.o (117KB) - TouchGFX硬件抽象层

---

**版本**: TouchGFX 4.25.0
**平台**: STM32H745I-DISCO
**更新日期**: 2025-06-29
**实现状态**: ✅ ADC1→仪表盘2控制已完成
**编译状态**: ✅ 编译成功
