# Test compilation script for STM32H745I project
Write-Host "Testing STM32H745I project compilation..."

# Set environment variables
$env:PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin;C:\ST\STM32CubeCLT_1.18.0\CMake\bin;C:\ST\STM32CubeCLT_1.18.0\Ninja\bin;" + $env:PATH

# Test GCC compiler
Write-Host "Testing GCC compiler..."
& "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe" --version

# Define compiler flags
$CFLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-d16", 
    "-mfloat-abi=hard",
    "-DUSE_HAL_DRIVER",
    "-DSTM32H745xx",
    "-DCORE_CM7",
    "-DUSE_PWR_DIRECT_SMPS_SUPPLY",
    "-I../CM7/Core/Inc",
    "-I../CM7/TouchGFX/App",
    "-I../CM7/TouchGFX/target/generated",
    "-I../CM7/TouchGFX/target",
    "-I../CM7/TouchGFX/gui/include",
    "-I../Drivers/STM32H7xx_HAL_Driver/Inc",
    "-I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy",
    "-I../Middlewares/Third_Party/FreeRTOS/Source/include",
    "-I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2",
    "-I../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F",
    "-I../Drivers/CMSIS/Device/ST/STM32H7xx/Include",
    "-I../Drivers/CMSIS/Include",
    "-O0",
    "-g3",
    "-Wall",
    "-fdata-sections",
    "-ffunction-sections",
    "-c"
)

# Test compile main.c
Write-Host "Testing main.c compilation..."
$cmd = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin\arm-none-eabi-gcc.exe"
$args = $CFLAGS + @("CM7/Core/Src/main.c", "-o", "test_main.o")

Write-Host "Command: $cmd $($args -join ' ')"
& $cmd @args

if ($LASTEXITCODE -eq 0) {
    Write-Host "main.c compilation successful!"
    if (Test-Path "test_main.o") {
        Remove-Item "test_main.o"
    }
} else {
    Write-Host "main.c compilation failed with exit code: $LASTEXITCODE"
}

# Test compile TouchGFX files
Write-Host "`nTesting TouchGFX Model.cpp compilation..."
$args = $CFLAGS + @("CM7/TouchGFX/gui/src/model/Model.cpp", "-o", "test_model.o")
& $cmd @args

if ($LASTEXITCODE -eq 0) {
    Write-Host "Model.cpp compilation successful!"
    if (Test-Path "test_model.o") {
        Remove-Item "test_model.o"
    }
} else {
    Write-Host "Model.cpp compilation failed with exit code: $LASTEXITCODE"
}

Write-Host "`nTesting Screen1Presenter.cpp compilation..."
$args = $CFLAGS + @("CM7/TouchGFX/gui/src/screen1_screen/Screen1Presenter.cpp", "-o", "test_presenter.o")
& $cmd @args

if ($LASTEXITCODE -eq 0) {
    Write-Host "Screen1Presenter.cpp compilation successful!"
    if (Test-Path "test_presenter.o") {
        Remove-Item "test_presenter.o"
    }
} else {
    Write-Host "Screen1Presenter.cpp compilation failed with exit code: $LASTEXITCODE"
}

Write-Host "`nCompilation test completed."
