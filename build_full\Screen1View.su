C:\Users\<USER>\STMicroelectronics\TouchGFX\4.25.0\touchgfx\framework\include/touchgfx/Screen.hpp:104:18:virtual void touchgfx::Screen::afterTransition()	0	static
C:\Users\<USER>\STMicroelectronics\TouchGFX\4.25.0\touchgfx\framework\include/touchgfx/Screen.hpp:144:18:virtual void touchgfx::Screen::handleTickEvent()	0	static
C:\Users\<USER>\STMicroelectronics\TouchGFX\4.25.0\touchgfx\framework\include/touchgfx/Screen.hpp:154:18:virtual void touchgfx::Screen::handleKeyEvent(uint8_t)	0	static
CM7/TouchGFX/generated/gui_generated/include/gui_generated/screen1_screen/Screen1ViewBase.hpp:25:18:virtual void Screen1ViewBase::action1()	0	static
CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp:13:6:virtual void Screen1View::tearDownScreen()	0	static
CM7/TouchGFX/gui/include/gui/screen1_screen/Screen1View.hpp:12:13:Screen1View::~Screen1View()	8	static
CM7/TouchGFX/gui/include/gui/screen1_screen/Screen1View.hpp:12:13:virtual Screen1View::~Screen1View()	8	static
CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp:3:1:Screen1View::Screen1View()	8	static
CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp:8:6:virtual void Screen1View::setupScreen()	0	static
CM7/TouchGFX/gui/src/screen1_screen/Screen1View.cpp:23:6:void Screen1View::updateGauge2(uint8_t)	8	static
